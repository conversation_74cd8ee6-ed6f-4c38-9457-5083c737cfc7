{"roster_id": "RozieAir", "time_zone": "America/New_York", "use_case_domain": "Airline", "company_name": "Rozie Airline", "avatar_name": "<PERSON>", "knowledge_base_agent": true, "knowledge_base_config": {"illuminar_config": {"base_url": "https://api-manager-sandbox.rozie.ai/event-adapter", "search_url": "/v1/adapters-illuminar", "application_id": "application_9f6d6202-f5d9-445d-b856-82b44844f0d4", "api_key": "5b5fadd08ddc4295abfa854244cbfbb2", "enhancement": false, "params": {"num_of_results": 2, "folders": ["Rozie <PERSON>"]}}, "function_map": [], "knowledge_base_topics": [], "knowledge_snippets": [], "instructions": ["When encountering an SSR (Special Service Request) code in a query, response, or document, do the following:", " 1. Always detect the SSR code (e.g., WCHR, VGML, UMNR)", " 2. append or display the full meaning of the code in parentheses next to it.", "This structure defines user topics (intents) and the specific follow-up entities you should ask for in order to gather more information and improve the accuracy of your response.\n\n intents:\n  baggage_allowance:\n    ask_to_improve_answer:\n      - origin_airport\n      - destination_airport\n      - fare_type\n      - aeroplan_status\n\n  baggage_size:\n    ask_to_improve_answer:\n      - fare_type\n      - airplane_type"]}, "triage_intelligence": {"confidence_thresholds": {"high": 0.85, "medium": 0.5, "low": 0.3}, "clarification_strategies": {"max_candidates": 3, "clarification_template": "I can help with {options}. Which would you prefer?", "follow_up_attempts": 2}, "unknown_use_case_handling": {"enabled": true, "handle_in_triage": true, "fallback_sequence": ["domain_alternatives", "out_of_scope_message", "human_handoff_if_enabled"], "domain_alternatives_message": "I'm {avatar_name}, your {use_case_domain} specialist. For {request_type}, that's outside my area. However, I can help you with {domain_services}. Is there anything about {use_case_domain} I can assist with?", "out_of_scope_message": "I'm the front desk for {company_name} {use_case_domain}. I can't help with {request_type}, but I can assist with our {use_case_domain} services.", "escalation_available": true}, "fallback_config": {"domain_alternatives": ["flight booking and reservations", "flight status and updates", "baggage tracking and policies", "check-in and boarding assistance", "special service requests"], "out_of_scope_template": "I'm <PERSON>, your Rozie Airlines specialist. For {request_type}, I'd recommend {alternative}. Is there anything about your flight or travel plans I can help with?", "escalation_message": "I want to ensure you get the best possible assistance. Let me connect you with a specialist who can properly address your specific needs."}, "stability_patterns": {"adaptive_routing": true, "conflict_resolution": "priority_based", "context_preservation": true, "progressive_learning": true}}, "workflow_routing_intelligence": {"RozieAir_Flight_Booking_Flow": {"triggers": ["searching for flights", "booking tickets", "flight reservations", "flight prices", "flight availability"], "confidence_indicators": ["specific dates mentioned", "destination mentioned", "passenger count specified"], "clarification_questions": ["Are you looking to book a new flight or modify an existing reservation?", "Do you need a one-way or round-trip ticket?"]}, "RozieAir_Flight_Status": {"triggers": ["flight status", "flight delay", "departure time", "arrival time", "flight information"], "confidence_indicators": ["flight number mentioned", "date specified", "specific flight reference"], "clarification_questions": ["Could you provide your flight number or departure details?", "Which specific flight would you like status information for?"]}, "Case_Status": {"triggers": ["case status", "support ticket", "complaint status", "case update", "ticket number"], "confidence_indicators": ["case number mentioned", "reference number provided", "previous support contact"], "clarification_questions": ["Do you have a case or reference number I can look up?", "When did you last contact our support team?"]}, "Name_Correction": {"triggers": ["name correction", "name change", "passenger name", "spelling error", "wrong name"], "confidence_indicators": ["booking reference provided", "current name vs correct name mentioned"], "clarification_questions": ["Do you need to correct the spelling of a passenger name?", "Could you provide your booking reference for the name correction?"]}, "Prebook_Meal": {"triggers": ["meal booking", "special meal", "dietary requirements", "food preferences", "in-flight meal"], "confidence_indicators": ["flight booking reference", "specific dietary requirement mentioned"], "clarification_questions": ["What type of special meal would you like to request?", "Do you have a booking reference for the flight?"]}, "Baggage_Support": {"triggers": ["baggage status", "lost baggage", "baggage tracking", "luggage issue", "baggage claim"], "confidence_indicators": ["baggage reference number", "flight details provided", "specific baggage issue described"], "clarification_questions": ["Is this about delayed, lost, or damaged baggage?", "Do you have a baggage reference number or flight details?"]}}, "intelligent_fallbacks": {"partial_domain_matches": {"travel_general": {"response": "I specialize in Rozie Airlines services. For general travel information, I'd recommend checking with your travel agent or the destination's tourism website. However, I can help you with flights, bookings, or airline-specific services. What can I assist you with today?", "alternatives": ["flight booking", "baggage policies", "check-in assistance"]}, "other_airlines": {"response": "I'm <PERSON> from Rozie Airlines and can only assist with Rozie flights and services. For other airlines, you'll need to contact them directly. Is there anything about your Rozie Airlines travel I can help with?", "alternatives": ["Rozie flight status", "Rozie bookings", "Rozie policies"]}}, "completely_out_of_scope": {"weather": "I can't provide weather information, but I can help you check if weather might affect your Rozie Airlines flights. Would you like me to check your flight status?", "general_questions": "I'm <PERSON>, your Rozie Airlines virtual assistant. I specialize in flight bookings, status updates, and travel services. How can I help with your airline needs today?", "other_services": "I focus exclusively on Rozie Airlines services. For {service_type}, you'd need to contact the appropriate provider directly. Is there anything about your Rozie travel I can assist with?"}}, "context_management": {"session_memory": true, "conversation_history_length": 10, "context_summarization": true, "cross_agent_context_transfer": true, "mid_conversation_routing": {"enabled": true, "context_preservation": true, "transition_messages": {"agent_switch": "I'm now connecting you with our {specialist_type} who can better assist you with {topic}.", "return_to_triage": "I'm back to help you with any other questions or if you need assistance with a different topic."}}}, "monitoring_and_improvement": {"routing_accuracy_tracking": true, "confidence_calibration": true, "clarification_success_rate": true, "fallback_analysis": true, "performance_thresholds": {"min_routing_accuracy": 0.95, "max_clarification_rate": 0.15, "max_fallback_rate": 0.05}, "adaptive_learning": {"enabled": true, "confidence_adjustment": true, "pattern_recognition": true, "workflow_optimization": true}}, "welcome_message": {"default": [{"type": "text", "message": "Welcome to Rozie Airlines! I'm <PERSON>, your intelligent travel assistant. I can help you with flight bookings, status updates, baggage tracking, and all your airline needs. How may I assist you today?"}]}, "workflows": {"RozieAir_Flight_Booking_Flow": ["If customer conversation is about searching for flights or booking tickets, then trigger this workflow. Searches 400+ airlines for best fares, supports one-way/multi-city routes, and provides flight details including prices"], "RozieAir_Flight_Status": ["If customer conversation is about checking the flight's status then trigger this workflow."], "Case_Status": ["If customer conversation is about update on customer's support case then trigger this workflow."], "Name_Correction": ["If customer conversation is about correcting their name in ticket then trigger this workflow."], "Prebook_Meal": ["If customer conversation is about pre booking the meal then trigger this workflow."], "Baggage_Support": ["If customer conversation is about checking the baggage's status then trigger this workflow."]}, "model": {"intent_detection_agent": "gpt-4.1", "response_builder_agent": "gpt-4.1", "conversation_status_agent": "gpt-4.1", "quick_response_builder_agent": "gpt-4.1", "memory_agent": "gpt-4.1", "Knowledge_Base": "gpt-4.1", "default": "gpt-4.1", "workflow_agent": "gpt-4.1", "triage_agent": "gpt-4.1"}}