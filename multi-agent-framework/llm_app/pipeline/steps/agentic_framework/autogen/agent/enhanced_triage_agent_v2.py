"""
ENHANCED TRIAGE AGENT V2.0
Integrates: Microsoft AutoGen Streaming + 12-Factor Principles + Our Medical Research
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime

# Import validation for dependencies
try:
    from .triage_intelligence import TriageIntelligenceEngine, TriageDecision, ConversationContext
    from .streaming_handoff_enhancement import EnhancedStreamingHandoffManager, StreamingHandoffEvent
    from ..utils.twelve_factor_validation import TwelveFactorAgentValidator
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Enhanced Triage V2.0 dependencies not available: {e}")
    print("📝 Falling back to basic triage functionality")
    DEPENDENCIES_AVAILABLE = False

class EnhancedTriageAgentV2:
    """
    Enhanced Triage Agent V2.0 with streaming handoffs and 12-factor compliance
    
    Combines:
    - Our medical research validated intelligence
    - Microsoft AutoGen streaming patterns
    - HumanLayer 12-Factor production principles
    """
    
    def __init__(self, config: Dict[str, Any], autogen_agent):
        self.config = config
        self.autogen_agent = autogen_agent
        
        if not DEPENDENCIES_AVAILABLE:
            print("⚠️  Creating basic triage agent - enhanced features unavailable")
            self.enhanced_mode = False
            return
            
        self.enhanced_mode = True
        
        # Initialize intelligence engine
        self.intelligence_engine = TriageIntelligenceEngine(config)
        
        # Initialize streaming handoff manager  
        self.streaming_manager = EnhancedStreamingHandoffManager(self)
        
        # Initialize 12-factor validator
        self.twelve_factor_validator = TwelveFactorAgentValidator(
            config, 
            {'prompt_system': 'enhanced_triage_prompts'}
        )
        
        # Performance tracking
        self.performance_metrics = {
            'total_interactions': 0,
            'successful_routings': 0,
            'clarifications_needed': 0,
            'fallbacks_triggered': 0,
            'streaming_handoffs': 0
        }
        
    async def process_user_request(self, user_message: str, conversation_history: List[str] = None) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Main entry point: Process user request with streaming response
        Implements: 12-Factor principles + streaming UX + medical research patterns
        """
        
        if conversation_history is None:
            conversation_history = []
            
        # Update metrics
        self.performance_metrics['total_interactions'] += 1
        
        try:
            # Step 1: Intelligent decision making (medical research validated)
            routing_decision = await self._make_intelligent_routing_decision(user_message, conversation_history)
            
            # Step 2: Stream appropriate flow based on confidence
            if routing_decision.confidence >= self.config.get('triage_intelligence', {}).get('confidence_thresholds', {}).get('high', 0.85):
                # High confidence: Direct streaming handoff
                async for event in self._stream_direct_handoff(routing_decision, conversation_history, user_message):
                    yield event
                    
            elif routing_decision.confidence >= self.config.get('triage_intelligence', {}).get('confidence_thresholds', {}).get('medium', 0.5):
                # Medium confidence: Stream clarification flow
                async for event in self._stream_clarification_flow(routing_decision, conversation_history, user_message):
                    yield event
                    
            else:
                # Low confidence: Stream fallback flow
                async for event in self._stream_fallback_flow(routing_decision, conversation_history, user_message):
                    yield event
                    
        except Exception as e:
            # Graceful error handling (12-Factor: Compact Errors)
            yield StreamingHandoffEvent(
                event_type="system_error",
                source_agent="Enhanced_Triage_Agent_V2",
                target_agent="Error_Handler",
                confidence=0.0,
                context_data={'error': str(e), 'recovery_action': 'human_escalation'},
                user_message="I encountered an issue. Let me connect you with our support team."
            )

    async def _make_intelligent_routing_decision(self, user_message: str, conversation_history: List[str]) -> TriageDecision:
        """
        Make intelligent routing decision using our medical research patterns
        Enhanced with: Multi-factor analysis + confidence scoring + context awareness
        """
        
        # Create conversation context
        context = ConversationContext(
            current_message=user_message,
            conversation_history=conversation_history,
            user_preferences={},
            session_metadata={}
        )
        
        # Use our enhanced intelligence engine
        decision = await self.intelligence_engine.analyze_request_with_context(context)
        
        return decision

    async def _stream_direct_handoff(self, decision: TriageDecision, history: List[str], user_message: str) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Stream direct handoff for high-confidence decisions
        Uses: Microsoft AutoGen streaming patterns
        """
        self.performance_metrics['successful_routings'] += 1
        self.performance_metrics['streaming_handoffs'] += 1
        
        routing_decision = {
            'target_workflow': decision.target_workflow,
            'confidence': decision.confidence, 
            'reasoning': decision.reasoning,
            'metadata': decision.metadata
        }
        
        async for event in self.streaming_manager.stream_intelligent_handoff(
            routing_decision, history, user_message):
            yield event

    async def _stream_clarification_flow(self, decision: TriageDecision, history: List[str], user_message: str) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Stream clarification flow for medium-confidence decisions
        Uses: IBM Watson clarification patterns + our intelligence
        """
        self.performance_metrics['clarifications_needed'] += 1
        
        # Generate clarification question using our intelligence engine
        clarification_data = await self.intelligence_engine.generate_clarification_question(decision)
        
        # Stream clarification process
        async for event in self.streaming_manager.stream_clarification_flow(clarification_data, history):
            yield event
            
        # Note: In production, would wait for user response and continue routing

    async def _stream_fallback_flow(self, decision: TriageDecision, history: List[str], user_message: str) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Stream fallback flow for low-confidence decisions  
        Uses: Three-tier fallback strategy + Galileo stability patterns
        """
        self.performance_metrics['fallbacks_triggered'] += 1
        
        # Generate fallback response using our intelligence engine
        fallback_data = await self.intelligence_engine.generate_fallback_response(decision)
        
        # Stream fallback process
        async for event in self.streaming_manager.stream_fallback_flow(fallback_data):
            yield event

    async def get_system_health_report(self) -> Dict[str, Any]:
        """
        Get comprehensive system health report
        Includes: Performance metrics + 12-Factor compliance + Intelligence metrics
        """
        
        # Get 12-Factor compliance report
        compliance_report = self.twelve_factor_validator.generate_compliance_report()
        
        # Get intelligence engine metrics
        intelligence_metrics = self.intelligence_engine.get_performance_metrics()
        
        # Get streaming handoff metrics
        handoff_metrics = self.streaming_manager.get_handoff_metrics()
        
        # Calculate derived metrics
        success_rate = (self.performance_metrics['successful_routings'] / 
                       max(self.performance_metrics['total_interactions'], 1))
        
        clarification_rate = (self.performance_metrics['clarifications_needed'] / 
                             max(self.performance_metrics['total_interactions'], 1))
        
        fallback_rate = (self.performance_metrics['fallbacks_triggered'] / 
                        max(self.performance_metrics['total_interactions'], 1))
        
        return {
            "system_status": "OPERATIONAL",
            "timestamp": datetime.now().isoformat(),
            "version": "Enhanced_Triage_Agent_V2.0",
            
            # Core performance metrics
            "performance_metrics": {
                **self.performance_metrics,
                "success_rate": round(success_rate, 3),
                "clarification_rate": round(clarification_rate, 3), 
                "fallback_rate": round(fallback_rate, 3)
            },
            
            # Expected vs actual performance (medical research targets)
            "target_performance": {
                "target_success_rate": 0.95,
                "target_clarification_rate": 0.15,
                "target_fallback_rate": 0.05,
                "performance_vs_target": {
                    "success_rate_delta": round(success_rate - 0.95, 3),
                    "clarification_rate_delta": round(clarification_rate - 0.15, 3),
                    "fallback_rate_delta": round(fallback_rate - 0.05, 3)
                }
            },
            
            # 12-Factor compliance
            "twelve_factor_compliance": compliance_report,
            
            # Intelligence engine metrics  
            "intelligence_metrics": intelligence_metrics,
            
            # Streaming handoff metrics
            "streaming_metrics": handoff_metrics,
            
            # System recommendations
            "recommendations": self._generate_system_recommendations(
                success_rate, clarification_rate, fallback_rate, compliance_report
            )
        }

    def _generate_system_recommendations(self, 
                                       success_rate: float, 
                                       clarification_rate: float,
                                       fallback_rate: float,
                                       compliance_report: Dict[str, Any]) -> List[str]:
        """Generate intelligent system recommendations"""
        
        recommendations = []
        
        # Performance-based recommendations
        if success_rate < 0.90:
            recommendations.append("🎯 Consider enhancing prompt engineering - success rate below target")
            
        if clarification_rate > 0.20:
            recommendations.append("❓ Review clarification strategies - rate higher than optimal")
            
        if fallback_rate > 0.10:
            recommendations.append("🚨 Investigate fallback triggers - rate above acceptable threshold")
            
        # Compliance-based recommendations  
        if compliance_report['overall_score'] < 0.8:
            recommendations.extend(compliance_report['top_recommendations'][:2])
            
        # System health recommendations
        if self.performance_metrics['streaming_handoffs'] < self.performance_metrics['successful_routings'] * 0.8:
            recommendations.append("🔄 Consider enabling streaming for more interactions")
            
        return recommendations

    def update_performance_metrics(self, metric_type: str, success: bool):
        """Update performance metrics for tracking"""
        if metric_type == 'routing' and success:
            self.performance_metrics['successful_routings'] += 1
        elif metric_type == 'clarification':
            self.performance_metrics['clarifications_needed'] += 1
        elif metric_type == 'fallback':
            self.performance_metrics['fallbacks_triggered'] += 1

    def log_interaction(self, interaction_type: str, details: str, confidence: float, outcome: str):
        """Log interaction for analysis and improvement"""
        # In production: Would log to monitoring system
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'interaction_type': interaction_type,
            'details': details,
            'confidence': confidence,
            'outcome': outcome
        }
        
        # Could integrate with logging service
        print(f"LOG: {json.dumps(log_entry, indent=2)}")

# Factory function for easy instantiation
def create_enhanced_triage_agent_v2(config: Dict[str, Any], autogen_agent) -> EnhancedTriageAgentV2:
    """
    Factory function to create Enhanced Triage Agent V2.0
    
    Integrates:
    - Medical research validated patterns
    - Microsoft AutoGen streaming capabilities  
    - HumanLayer 12-Factor production principles
    """
    return EnhancedTriageAgentV2(config, autogen_agent) 