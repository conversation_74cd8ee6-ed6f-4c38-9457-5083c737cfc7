"""
STREAMING HANDOFF ENHANCEMENT
Based on: Microsoft AutoGen Core Streaming Handoffs FastAPI
Integrates with: Our Enhanced Triage Intelligence System
"""

import asyncio
import json
from typing import AsyncGenerator, Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

@dataclass
class StreamingHandoffEvent:
    """Event structure for streaming agent handoffs"""
    event_type: str  # "handoff_start", "context_transfer", "agent_ready", "handoff_complete"
    source_agent: str
    target_agent: str
    confidence: float
    context_data: Dict[str, Any]
    user_message: Optional[str] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class EnhancedStreamingHandoffManager:
    """
    Streaming handoff manager integrating with our enhanced triage intelligence
    Based on Microsoft AutoGen patterns + our medical research validation
    """
    
    def __init__(self, triage_wrapper):
        self.triage_wrapper = triage_wrapper
        self.active_handoffs = {}
        self.context_preservation_enabled = True
        
    async def stream_intelligent_handoff(self, 
                                       routing_decision: Dict[str, Any], 
                                       conversation_context: List[str],
                                       user_query: str) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Stream intelligent handoff with confidence-based routing
        Combines: Microsoft AutoGen streaming + Our confidence scoring
        """
        handoff_id = f"handoff_{datetime.now().timestamp()}"
        self.active_handoffs[handoff_id] = {
            'start_time': datetime.now(),
            'source_agent': 'Enhanced_Triage_Agent',
            'target_workflow': routing_decision.get('target_workflow'),
            'confidence': routing_decision.get('confidence', 0.0)
        }
        
        try:
            # Step 1: Announce handoff start with confidence
            yield StreamingHandoffEvent(
                event_type="handoff_start",
                source_agent="Enhanced_Triage_Agent",
                target_agent=routing_decision['target_workflow'],
                confidence=routing_decision['confidence'],
                context_data={'reasoning': routing_decision.get('reasoning', '')},
                user_message=f"I'm connecting you with our {self._get_specialist_name(routing_decision['target_workflow'])} "
                             f"(confidence: {routing_decision['confidence']:.1%})"
            )
            
            await asyncio.sleep(0.1)  # Smooth UX timing
            
            # Step 2: Stream context transfer (12-Factor: Own your context window)
            context_summary = self._generate_rich_context_summary(conversation_context, user_query)
            yield StreamingHandoffEvent(
                event_type="context_transfer",
                source_agent="Enhanced_Triage_Agent", 
                target_agent=routing_decision['target_workflow'],
                confidence=routing_decision['confidence'],
                context_data={
                    'context_summary': context_summary,
                    'user_preferences': {},
                    'conversation_history': conversation_context[-3:],  # Last 3 exchanges
                    'routing_metadata': routing_decision.get('metadata', {})
                },
                user_message="Transferring your conversation context..."
            )
            
            await asyncio.sleep(0.2)
            
            # Step 3: Target agent ready (with intelligence validation)
            agent_readiness = await self._validate_target_agent_readiness(routing_decision['target_workflow'])
            yield StreamingHandoffEvent(
                event_type="agent_ready",
                source_agent="Enhanced_Triage_Agent",
                target_agent=routing_decision['target_workflow'],
                confidence=routing_decision['confidence'],
                context_data={
                    'agent_capabilities': agent_readiness['capabilities'],
                    'expected_resolution_time': agent_readiness['resolution_time']
                },
                user_message=f"Our {self._get_specialist_name(routing_decision['target_workflow'])} is ready to assist you!"
            )
            
            await asyncio.sleep(0.1)
            
            # Step 4: Complete handoff with success metrics
            self._log_handoff_success(handoff_id, routing_decision)
            yield StreamingHandoffEvent(
                event_type="handoff_complete",
                source_agent="Enhanced_Triage_Agent",
                target_agent=routing_decision['target_workflow'],
                confidence=routing_decision['confidence'],
                context_data={
                    'handoff_duration': (datetime.now() - self.active_handoffs[handoff_id]['start_time']).total_seconds(),
                    'success': True,
                    'next_steps': self._get_next_steps(routing_decision['target_workflow'])
                },
                user_message="Handoff complete! You're now speaking with our specialist."
            )
            
        except Exception as e:
            # Graceful error handling (12-Factor: Compact Errors into Context Window)
            yield StreamingHandoffEvent(
                event_type="handoff_error",
                source_agent="Enhanced_Triage_Agent",
                target_agent=routing_decision.get('target_workflow', 'unknown'),
                confidence=0.0,
                context_data={'error': str(e)},
                user_message="I apologize for the delay. Let me connect you directly with our support team."
            )
        finally:
            # Cleanup
            if handoff_id in self.active_handoffs:
                del self.active_handoffs[handoff_id]

    async def stream_clarification_flow(self, 
                                      clarification_data: Dict[str, Any],
                                      conversation_context: List[str]) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Stream clarification process with intelligent question generation
        Uses: IBM Watson patterns + our confidence scoring
        """
        yield StreamingHandoffEvent(
            event_type="clarification_start",
            source_agent="Enhanced_Triage_Agent",
            target_agent="Enhanced_Triage_Agent",  # Stay with triage for clarification
            confidence=clarification_data['confidence'],
            context_data={
                'candidate_workflows': clarification_data.get('candidate_workflows', []),
                'clarification_type': 'intelligent_routing'
            },
            user_message="Let me ask a quick question to route you to the right specialist..."
        )
        
        await asyncio.sleep(0.1)
        
        yield StreamingHandoffEvent(
            event_type="clarification_question",
            source_agent="Enhanced_Triage_Agent",
            target_agent="Enhanced_Triage_Agent",
            confidence=clarification_data['confidence'],
            context_data={
                'question_type': 'binary_choice' if len(clarification_data.get('candidate_workflows', [])) == 2 else 'multiple_choice',
                'reasoning': clarification_data.get('reasoning', '')
            },
            user_message=clarification_data['clarification_question']
        )

    async def stream_fallback_flow(self, 
                                 fallback_data: Dict[str, Any]) -> AsyncGenerator[StreamingHandoffEvent, None]:
        """
        Stream graceful fallback with three-tier strategy
        Uses: Galileo stability patterns + our fallback intelligence
        """
        fallback_type = fallback_data.get('fallback_type', 'general')
        
        yield StreamingHandoffEvent(
            event_type="fallback_start",
            source_agent="Enhanced_Triage_Agent",
            target_agent="Human_Specialist",
            confidence=fallback_data['confidence'],
            context_data={
                'fallback_tier': self._determine_fallback_tier(fallback_type),
                'alternatives_offered': True
            },
            user_message="I want to make sure you get the best help possible..."
        )
        
        await asyncio.sleep(0.2)
        
        yield StreamingHandoffEvent(
            event_type="fallback_response",
            source_agent="Enhanced_Triage_Agent", 
            target_agent="Human_Specialist",
            confidence=fallback_data['confidence'],
            context_data={
                'fallback_type': fallback_type,
                'escalation_reason': fallback_data.get('reasoning', 'Out of scope request')
            },
            user_message=fallback_data['fallback_response']
        )

    def _generate_rich_context_summary(self, conversation_context: List[str], current_query: str) -> str:
        """Generate comprehensive context summary for handoff"""
        if not conversation_context:
            return f"New conversation starting with: {current_query[:100]}..."
        
        # Extract key information (could be enhanced with NLP)
        recent_context = " ".join(conversation_context[-3:])
        return f"Context: {recent_context[:200]}... | Current request: {current_query[:100]}..."

    async def _validate_target_agent_readiness(self, target_workflow: str) -> Dict[str, Any]:
        """Validate target agent is ready (could integrate with agent health checks)"""
        # Simulate agent readiness check
        await asyncio.sleep(0.1)
        
        capabilities = {
            'RozieAir_Flight_Status': ['flight tracking', 'delay information', 'schedule updates'],
            'RozieAir_Flight_Booking_Flow': ['flight search', 'booking creation', 'pricing'],
            'Baggage_Support': ['tracking', 'claims', 'policy information']
        }
        
        return {
            'capabilities': capabilities.get(target_workflow, ['general assistance']),
            'resolution_time': '2-3 minutes',
            'ready': True
        }

    def _get_specialist_name(self, workflow: str) -> str:
        """Get user-friendly specialist names"""
        names = {
            'RozieAir_Flight_Status': 'flight status specialist',
            'RozieAir_Flight_Booking_Flow': 'booking specialist', 
            'Baggage_Support': 'baggage specialist',
            'Case_Status': 'support case specialist'
        }
        return names.get(workflow, 'specialist')

    def _get_next_steps(self, workflow: str) -> List[str]:
        """Provide helpful next steps for each workflow"""
        steps = {
            'RozieAir_Flight_Status': [
                'Provide your flight number or departure details',
                'Mention any specific concerns about your flight'
            ],
            'RozieAir_Flight_Booking_Flow': [
                'Share your travel dates and destinations',
                'Let us know passenger count and preferences'
            ]
        }
        return steps.get(workflow, ['Describe your specific needs'])

    def _determine_fallback_tier(self, fallback_type: str) -> int:
        """Determine fallback tier based on our three-tier strategy"""
        tier_mapping = {
            'domain_alternative': 1,
            'out_of_scope': 2, 
            'escalation': 3
        }
        return tier_mapping.get(fallback_type, 2)

    def _log_handoff_success(self, handoff_id: str, routing_decision: Dict[str, Any]):
        """Log successful handoff for performance tracking"""
        if self.triage_wrapper:
            self.triage_wrapper.update_performance_metrics('routing', True)
            self.triage_wrapper.log_interaction(
                'streaming_handoff',
                f"Handoff to {routing_decision['target_workflow']}",
                routing_decision['confidence'],
                'successful_handoff'
            )

    def get_handoff_metrics(self) -> Dict[str, Any]:
        """Get handoff performance metrics"""
        return {
            'active_handoffs': len(self.active_handoffs),
            'total_handoffs_today': 0,  # Could integrate with metrics system
            'avg_handoff_time': 0.5,   # Could track actual timing
            'success_rate': 0.98       # Could track from logs
        } 