"""
Enhanced Triage Agent V2 - <PERSON><PERSON><PERSON>'s Requirements Implementation
================================================================

This module implements the enhanced triage agent following <PERSON><PERSON><PERSON>'s specific requirements:
- Talk-back conversation capability
- Clarification for ambiguous requests
- Unknown-info handling with graceful fallbacks
- Mid-conversation transfer support
- Simple conversational responses (no complex JSON)
"""

import types
from autogen_core.models import SystemMessage
from autogen_core.tools import FunctionTool
from autogen_core import TypeSubscription

from llm_app.pipeline.steps.agentic_framework.autogen.interfaces.agent_base import AIAgent
from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import (
    _get_triage_agent_v2_system_prompt
)


class TriageAgentV2Wrapper:
    """
    Enhanced Triage Agent V2 following <PERSON><PERSON><PERSON>'s requirements.

    Key Features:
    - Conversational responses before routing decisions
    - Clarification questions for ambiguous requests (max 2 attempts)
    - Unknown-info handling merged into triage capability
    - Out-of-scope graceful fallbacks
    - Mid-conversation transfer support
    """

    def __init__(
        self,
        *,
        config: dict,
        runtime,
        model_client,
        response_queue,
        user_topic_type,
        toolset: list = None,
        delegate_tools: list = None,
    ):
        """
        Initialize the enhanced triage agent.

        Args:
            config (dict): Configuration containing roster parameters
            runtime: Agent runtime
            model_client: LLM model client
            response_queue: Response queue for streaming
            user_topic_type: User agent topic type
            toolset: Available tools (optional)
            delegate_tools: Delegate tools for routing (optional)
        """
        self.config = config
        self.runtime = runtime
        self.model_client = model_client
        self.response_queue = response_queue
        self.user_topic_type = user_topic_type
        self.toolset = toolset or []
        self.delegate_tools = delegate_tools or []

        self.agent_topic_type = "Triage_Agent_V2"
        self.system_message = SystemMessage(content=_get_triage_agent_v2_system_prompt(config.copy()))
        self.description = "Enhanced triage coordinator with conversational capabilities, clarification support, and graceful fallbacks following Shubham's requirements."
    
    async def create(self):
        """Create and register the enhanced triage agent."""
        agent_type = await AIAgent.register(
            self.runtime,
            type=self.agent_topic_type,
            factory=lambda: AIAgent(
                description=self.description,
                system_message=self.system_message,
                model_client=self.model_client,
                tools=self.toolset,
                delegate_tools=self.delegate_tools,
                agent_topic_type=self.agent_topic_type,
                user_topic_type=self.user_topic_type,
                response_queue=self.response_queue,
            ),
        )

        await self.runtime.add_subscription(
            TypeSubscription(topic_type=self.agent_topic_type, agent_type=agent_type.type)
        )
        return agent_type

    def get_delegate_tool(self):
        """Create delegate tool for routing back to this triage agent."""
        agent_role = "Enhanced triage coordinator with conversational capabilities and graceful fallbacks."
        agent_name = "Triage_Agent_V2"
        agent_topic = self.agent_topic_type

        # Generate a safe function name from agent_name
        func_name = f"route_to_{agent_name.lower().replace(' ', '_')}"

        # Define a closure-compatible function
        def _route_fn() -> str:
            return agent_topic

        # Create a dynamic function with the proper closure
        code = _route_fn.__code__
        globals_dict = globals()
        name = func_name
        argdefs = _route_fn.__defaults__
        closure = _route_fn.__closure__

        # Construct the new function
        route_fn = types.FunctionType(code, globals_dict, name, argdefs, closure)

        return FunctionTool(
            route_fn,
            description=(
                f"Use this tool if the customer needs clarification, has an ambiguous request, "
                f"or requires conversational assistance. This will route to **{agent_name}** "
                f"which provides enhanced triage with talk-back capabilities."
            ),
        )

