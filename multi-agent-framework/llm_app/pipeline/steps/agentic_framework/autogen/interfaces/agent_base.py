import json
import asyncio
from typing import List
from loguru import logger
from autogen_core import (
    <PERSON><PERSON>Call,
    MessageContext,
    RoutedAgent,
    TopicId,
    message_handler,
)
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    SystemMessage,
)
from autogen_core.tools import Tool
from llm_app.pipeline.steps.agentic_framework.autogen.models.models import UserTask, AgentResponse
from llm_app.pipeline.steps.agentic_framework.autogen.session.conversation_session import ConversationSession


class AIAgent(RoutedAgent):
    def __init__(
        self,
        description: str,
        system_message: SystemMessage,
        model_client: ChatCompletionClient,
        tools: List[Tool],
        delegate_tools: List[Tool],
        agent_topic_type: str,
        user_topic_type: str,
        response_queue: asyncio.Queue[str | object],
    ) -> None:
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        self._tools = dict([(tool["tool_name"], tool) for tool in tools])
        self._tool_schema = [tool["schema"] for tool in tools]
        self._delegate_tools = dict([(tool.name, tool) for tool in delegate_tools])
        self._delegate_tool_schema = [tool.schema for tool in delegate_tools]
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        self._response_queue = response_queue

    @message_handler
    async def handle_task(self, message: UserTask, ctx: MessageContext) -> None:
        try:
            # DEBUG: Print incoming message and agent context
            conversation_messages = await message.conversation_session_context.get_conversation_messages()
            print(f"\n🤖 AGENT DEBUG - MESSAGE PROCESSING")
            print(f"🏷️  Agent Type: {self._agent_topic_type}")
            print(f"📨 Conversation Messages Count: {len(conversation_messages)}")
            if conversation_messages:
                latest_msg = conversation_messages[-1]
                print(f"📝 Latest Message: {latest_msg.content[:100]}..." if len(latest_msg.content) > 100 else f"📝 Latest Message: {latest_msg.content}")
            print(f"🔧 Available Tools: {len(self._tool_schema)}")
            print(f"🔄 Delegate Tools: {len(self._delegate_tool_schema)}")
            print("-" * 60)

            # Start streaming LLM responses
            final_response = await self._model_client.create(
                messages=[self._system_message]
                + conversation_messages,
                tools=self._tool_schema + self._delegate_tool_schema,
                cancellation_token=ctx.cancellation_token,
            )
            # DEBUG: Print LLM response details
            print(f"💭 LLM Response - Thought: {final_response.thought[:100] if final_response.thought else 'None'}...")
            print(f"🔧 LLM Response - Tool Calls: {len(final_response.content) if hasattr(final_response, 'content') and final_response.content else 0}")

            # Process function/tool calls if present
            if final_response.thought:
                print(f"📤 Sending thought response: {final_response.thought[:100]}...")
                await self._response_queue.put(
                    {
                        "response_type": "text",
                        "text": final_response.thought,
                        "template": None,
                        "partial_response": True,
                        "should_end_interaction": False,
                        "is_forced_text_template": False,
                        "source": self._agent_topic_type,
                    }
                )
            while isinstance(final_response.content, list) and all(
                isinstance(m, FunctionCall) for m in final_response.content
            ):
                tool_call_results = []
                delegate_targets = []

                for call in final_response.content:
                    arguments = json.loads(call.arguments)
                    arguments["context_arguments"] = message.context_arguments

                    # DEBUG: Print tool call details
                    print(f"🔧 TOOL CALL - Name: {call.name}")
                    print(f"🔧 TOOL CALL - Arguments: {arguments}")
                    print(f"🔧 TOOL CALL - Is Regular Tool: {call.name in self._tools}")
                    print(f"🔧 TOOL CALL - Is Delegate Tool: {call.name in self._delegate_tools}")

                    if call.name in self._tools:
                        print(f"⚙️  Executing regular tool: {call.name}")
                        result = await self._tools[call.name]["tool"].run_json(arguments, ctx.cancellation_token)
                        result_str = json.dumps(result)
                        print(f"✅ Tool result: {result_str[:100]}...")

                        tool_call_results.append(
                            FunctionExecutionResult(call_id=call.id, content=result_str, is_error=False, name=call.name)
                        )
                    elif call.name in self._delegate_tools:
                        print(f"🔄 Executing delegate tool: {call.name}")
                        result = await self._delegate_tools[call.name].run_json(arguments, ctx.cancellation_token)
                        topic_type = self._delegate_tools[call.name].return_value_as_string(result)
                        print(f"🎯 Delegation target: {topic_type}")

                        await message.conversation_session_context.add_message(
                            AssistantMessage(content=[call], source=self.id.type)
                        )
                        await message.conversation_session_context.add_message(
                            FunctionExecutionResultMessage(
                                content=[
                                    FunctionExecutionResult(
                                        call_id=call.id,
                                        content=f"Transferred to {topic_type}. Adopt persona immediately.",
                                        is_error=False,
                                        name=call.name,
                                    )
                                ]
                            )
                        )
                        delegate_targets.append(
                            (
                                topic_type,
                                UserTask(
                                    conversation_session_context=message.conversation_session_context,
                                    context_arguments=message.context_arguments,
                                ),
                            )
                        )
                    else:
                        raise Exception(f"Unknown tool: {call.name}")

                # Handle delegation
                if delegate_targets:
                    print(f"🚀 DELEGATION - Starting delegation process")
                    print(f"🚀 DELEGATION - Number of targets: {len(delegate_targets)}")
                    for topic_type, task in delegate_targets:
                        print(f"🚀 DELEGATION - Transferring from {self._agent_topic_type} to {topic_type}")
                        await self.publish_message(task, topic_id=TopicId(topic_type, source=self.id.key))
                    print(f"🚀 DELEGATION - Transfer complete, ending current agent processing")
                    return  # Done after delegation

                # Handle follow-up call after tool execution
                if tool_call_results:
                    await message.conversation_session_context.add_message(
                        AssistantMessage(content=final_response.content, source=self.id.type)
                    )
                    await message.conversation_session_context.add_message(
                        FunctionExecutionResultMessage(content=tool_call_results)
                    )
                    for tool_call_result in tool_call_results:
                        if self._tools[tool_call_result.name].get("template"):
                            await self._response_queue.put(
                                {
                                    "response_type": "template",
                                    "text": None,
                                    "template": {
                                        "template_name": self._tools[tool_call_result.name].get("template"),
                                        "template_values": json.loads(tool_call_result.content),
                                    },
                                    "partial_response": False,
                                    "should_end_interaction": False,
                                    "is_forced_text_template": False,
                                    "source": self._agent_topic_type,
                                }
                            )

                    final_response = await self._model_client.create(
                        messages=[self._system_message]
                        + await message.conversation_session_context.get_conversation_messages(),
                        tools=self._tool_schema + self._delegate_tool_schema,
                        cancellation_token=ctx.cancellation_token,
                    )
                else:
                    return

            # Final message
            assert isinstance(final_response.content, str)
            print(f"📝 FINAL RESPONSE - Agent: {self._agent_topic_type}")
            print(f"📝 FINAL RESPONSE - Content: {final_response.content[:200]}...")
            print(f"📝 FINAL RESPONSE - Sending to user topic: {self._user_topic_type}")

            # Process triage agent JSON responses
            user_response_text = final_response.content
            if self._agent_topic_type == "Triage_Agent":
                try:
                    # Try to parse as JSON for triage agent responses
                    import re
                    # Extract JSON from potential code blocks
                    json_match = re.search(r'```json\s*(\{.*?\})\s*```', final_response.content, re.DOTALL)
                    if json_match:
                        json_content = json_match.group(1)
                        triage_data = json.loads(json_content)
                        
                        # Handle unknown_case action directly in triage
                        if isinstance(triage_data, dict):
                            if triage_data.get("action") == "unknown_case":
                                # Unknown case handled directly in triage - no routing needed
                                user_response_text = triage_data.get("response", final_response.content)
                                print(f"🎯 UNKNOWN CASE HANDLED - Tier: {triage_data.get('unknown_case_tier', 'unknown')}")
                                print(f"🎯 UNKNOWN CASE HANDLED - Response: {user_response_text[:100]}...")
                            elif "response" in triage_data:
                                # Extract user response for other actions
                                user_response_text = triage_data["response"]
                                print(f"🎯 TRIAGE JSON DETECTED - Extracted user response: {user_response_text[:100]}...")
                                print(f"🎯 TRIAGE JSON DETECTED - Full context preserved for internal processing")
                            else:
                                print(f"⚠️ TRIAGE JSON DETECTED - No 'response' field found, using full content")
                    else:
                        print(f"📝 TRIAGE AGENT - No JSON detected, using content as-is")
                except (json.JSONDecodeError, AttributeError) as e:
                    print(f"⚠️ TRIAGE JSON PARSE ERROR - Using content as-is: {e}")
                    # If JSON parsing fails, use the original content

            await message.conversation_session_context.add_message(
                AssistantMessage(content=final_response.content, source=self.id.type)
            )
            await self.publish_message(
                AgentResponse(content=final_response.content, reply_to_topic_type=self._agent_topic_type),
                topic_id=TopicId(self._user_topic_type, source=self.id.key),
            )

            await self._response_queue.put(
                {
                    "response_type": "text",
                    "text": user_response_text,  # Use extracted text for user, full content preserved in conversation
                    "template": None,
                    "partial_response": False,
                    "should_end_interaction": False,
                    "is_forced_text_template": False,
                    "source": self._agent_topic_type,
                }
            )
        except Exception as exception:
            logger.exception(
                f"unhandled exception in agent {self._agent_topic_type}: {exception}",
                status="FAILURE",
            )
            await self._response_queue.put(
                {
                    "response_type": "error",
                    "error": str(exception),
                    "source": self._agent_topic_type,
                }
            )
