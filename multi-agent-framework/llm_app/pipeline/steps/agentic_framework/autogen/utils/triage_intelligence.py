"""
ULTIMATE TRIAGE INTELLIGENCE MODULE
Combines: Medical Research + Galileo Stability + Industry Best Practices
"""

import json
import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging

@dataclass
class TriageDecision:
    """Structured triage decision with confidence and reasoning"""
    action: str  # "route", "clarify", "fallback"
    confidence: float
    reasoning: str
    target_workflow: Optional[str] = None
    clarification_question: Optional[str] = None
    fallback_response: Optional[str] = None
    context_summary: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ConversationContext:
    """Rich conversation context for intelligent routing"""
    history: List[str]
    user_preferences: Dict[str, Any]
    session_state: Dict[str, Any]
    previous_clarifications: List[str]
    escalation_attempts: int
    domain_context: str

class TriageIntelligenceEngine:
    """
    Ultimate Triage Intelligence Engine
    Powered by: MMedAgent-RL + Galileo Stability + IBM Watson Patterns
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.triage_intelligence = config.get('triage_intelligence', {})
        self.workflow_routing = config.get('workflow_routing_intelligence', {})
        self.fallback_config = config.get('intelligent_fallbacks', {})
        
        # Medical research validated thresholds
        self.confidence_thresholds = self.triage_intelligence.get('confidence_thresholds', {
            'high': 0.85,
            'medium': 0.5,
            'low': 0.3
        })
        
        # Galileo stability patterns
        self.stability_patterns = self.triage_intelligence.get('stability_patterns', {})
        
        # Performance tracking
        self.metrics = {
            'total_decisions': 0,
            'routing_decisions': 0,
            'clarification_decisions': 0,
            'fallback_decisions': 0,
            'confidence_distribution': {'high': 0, 'medium': 0, 'low': 0},
            'success_rate': 0.0
        }
        
        self.logger = logging.getLogger(__name__)

    def make_triage_decision(self, 
                           user_query: str, 
                           context: ConversationContext) -> TriageDecision:
        """
        Make intelligent triage decision using multi-factor analysis
        Combines: Intent detection + Confidence scoring + Context awareness
        """
        # Step 1: Analyze intent with confidence scoring
        intent_analysis = self._analyze_intent_with_confidence(user_query, context)
        
        # Step 2: Apply decision framework
        decision = self._apply_decision_framework(intent_analysis, user_query, context)
        
        # Step 3: Apply stability patterns
        decision = self._apply_stability_patterns(decision, context)
        
        # Step 4: Update metrics
        self._update_metrics(decision)
        
        # Step 5: Log decision
        self._log_decision(user_query, decision)
        
        return decision

    def _analyze_intent_with_confidence(self, 
                                      user_query: str, 
                                      context: ConversationContext) -> Dict[str, Any]:
        """
        Advanced intent analysis with medical research validated confidence scoring
        """
        analysis = {
            'matched_workflows': [],
            'confidence_scores': {},
            'intent_indicators': [],
            'ambiguity_factors': [],
            'context_factors': []
        }
        
        # Workflow matching with confidence calculation
        for workflow_name, workflow_data in self.workflow_routing.items():
            confidence = self._calculate_workflow_confidence(user_query, workflow_data, context)
            if confidence > 0.1:  # Only consider non-trivial matches
                analysis['matched_workflows'].append(workflow_name)
                analysis['confidence_scores'][workflow_name] = confidence
        
        # Sort by confidence
        analysis['matched_workflows'].sort(
            key=lambda x: analysis['confidence_scores'][x], 
            reverse=True
        )
        
        # Detect ambiguity factors
        analysis['ambiguity_factors'] = self._detect_ambiguity_factors(
            user_query, analysis['matched_workflows'], analysis['confidence_scores']
        )
        
        # Context enhancement
        analysis['context_factors'] = self._analyze_context_factors(context)
        
        return analysis

    def _calculate_workflow_confidence(self, 
                                     user_query: str, 
                                     workflow_data: Dict[str, Any], 
                                     context: ConversationContext) -> float:
        """
        Calculate confidence score using medical research patterns
        Factors: Keyword matching + Context consistency + Specificity + Intent clarity
        """
        confidence = 0.0
        query_lower = user_query.lower()
        
        # Factor 1: Trigger word matching (40% weight)
        triggers = workflow_data.get('triggers', [])
        trigger_matches = sum(1 for trigger in triggers if trigger.lower() in query_lower)
        if triggers:
            trigger_confidence = (trigger_matches / len(triggers)) * 0.4
            confidence += trigger_confidence
        
        # Factor 2: Confidence indicators (30% weight)  
        indicators = workflow_data.get('confidence_indicators', [])
        indicator_matches = sum(1 for indicator in indicators if indicator.lower() in query_lower)
        if indicators:
            indicator_confidence = (indicator_matches / len(indicators)) * 0.3
            confidence += indicator_confidence
        
        # Factor 3: Context consistency (20% weight)
        if context.history:
            context_relevance = self._calculate_context_relevance(query_lower, context.history)
            confidence += context_relevance * 0.2
        
        # Factor 4: Query specificity (10% weight)
        specificity_score = self._calculate_query_specificity(user_query)
        confidence += specificity_score * 0.1
        
        return min(confidence, 1.0)

    def _calculate_context_relevance(self, query: str, history: List[str]) -> float:
        """Calculate how well the query fits with conversation history"""
        if not history:
            return 0.5  # Neutral for new conversations
        
        recent_history = " ".join(history[-3:]).lower()  # Last 3 messages
        
        # Simple keyword overlap
        query_words = set(query.split())
        history_words = set(recent_history.split())
        
        if not query_words:
            return 0.0
        
        overlap = len(query_words.intersection(history_words))
        return min(overlap / len(query_words), 1.0)

    def _calculate_query_specificity(self, query: str) -> float:
        """Calculate query specificity based on detail level"""
        specificity_indicators = [
            r'\b\d{4,}\b',  # Numbers (flight numbers, reference codes)
            r'\b[A-Z]{3}\b',  # Airport codes
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # Dates
            r'\b\d{1,2}:\d{2}\b',  # Times
            r'\b[A-Z]{6}\b',  # Booking references
        ]
        
        specificity_score = 0.0
        for pattern in specificity_indicators:
            if re.search(pattern, query):
                specificity_score += 0.2
        
        # Longer queries tend to be more specific
        if len(query.split()) > 8:
            specificity_score += 0.2
        elif len(query.split()) > 5:
            specificity_score += 0.1
        
        return min(specificity_score, 1.0)

    def _detect_ambiguity_factors(self, 
                                 query: str, 
                                 matched_workflows: List[str], 
                                 confidence_scores: Dict[str, float]) -> List[str]:
        """Detect factors that make the intent ambiguous"""
        ambiguity_factors = []
        
        # Multiple high-confidence matches
        high_confidence_matches = [
            wf for wf in matched_workflows 
            if confidence_scores[wf] >= self.confidence_thresholds['medium']
        ]
        
        if len(high_confidence_matches) > 1:
            max_conf = max(confidence_scores[wf] for wf in high_confidence_matches)
            close_matches = [
                wf for wf in high_confidence_matches 
                if max_conf - confidence_scores[wf] < 0.15
            ]
            if len(close_matches) > 1:
                ambiguity_factors.append("multiple_similar_confidence_workflows")
        
        # Vague language indicators
        vague_terms = ['help', 'issue', 'problem', 'something', 'thing', 'stuff']
        if any(term in query.lower() for term in vague_terms):
            ambiguity_factors.append("vague_language")
        
        # Very short queries
        if len(query.split()) < 3:
            ambiguity_factors.append("insufficient_detail")
        
        return ambiguity_factors

    def _analyze_context_factors(self, context: ConversationContext) -> List[str]:
        """Analyze conversation context for decision enhancement"""
        factors = []
        
        if context.previous_clarifications:
            factors.append("previous_clarifications_attempted")
        
        if context.escalation_attempts > 0:
            factors.append("previous_escalation_attempts")
        
        if len(context.history) > 5:
            factors.append("extended_conversation")
        
        if context.user_preferences:
            factors.append("user_preferences_available")
        
        return factors

    def _apply_decision_framework(self, 
                                 intent_analysis: Dict[str, Any], 
                                 user_query: str, 
                                 context: ConversationContext) -> TriageDecision:
        """
        Apply the medical research validated decision framework
        High Confidence (≥0.85): Route immediately
        Medium Confidence (0.5-0.84): Clarify
        Low Confidence (<0.5): Fallback
        """
        matched_workflows = intent_analysis['matched_workflows']
        confidence_scores = intent_analysis['confidence_scores']
        ambiguity_factors = intent_analysis['ambiguity_factors']
        
        if not matched_workflows:
            # No workflow matches - fallback
            return TriageDecision(
                action="fallback",
                confidence=0.0,
                reasoning="No matching workflows found",
                fallback_response=self._generate_fallback_response(user_query, "out_of_scope"),
                metadata={"fallback_type": "no_match"}
            )
        
        top_workflow = matched_workflows[0]
        top_confidence = confidence_scores[top_workflow]
        
        # High confidence - route immediately
        if top_confidence >= self.confidence_thresholds['high'] and not ambiguity_factors:
            return TriageDecision(
                action="route",
                confidence=top_confidence,
                reasoning=f"High confidence match for {top_workflow}",
                target_workflow=top_workflow,
                context_summary=self._generate_context_summary(user_query, context),
                metadata={"routing_type": "high_confidence"}
            )
        
        # Medium confidence or ambiguous - clarify
        elif (top_confidence >= self.confidence_thresholds['medium'] or 
              len(matched_workflows) > 1):
            
            clarification_question = self._generate_clarification_question(
                matched_workflows[:3], user_query, context
            )
            
            return TriageDecision(
                action="clarify",
                confidence=top_confidence,
                reasoning=f"Clarification needed: {', '.join(ambiguity_factors)}",
                clarification_question=clarification_question,
                metadata={
                    "candidate_workflows": matched_workflows[:3],
                    "ambiguity_factors": ambiguity_factors
                }
            )
        
        # Low confidence - fallback
        else:
            fallback_type = "domain_alternative" if top_confidence > 0.2 else "out_of_scope"
            return TriageDecision(
                action="fallback",
                confidence=top_confidence,
                reasoning=f"Low confidence ({top_confidence:.2f}) for best match",
                fallback_response=self._generate_fallback_response(user_query, fallback_type),
                metadata={"fallback_type": fallback_type, "best_match": top_workflow}
            )

    def _apply_stability_patterns(self, 
                                 decision: TriageDecision, 
                                 context: ConversationContext) -> TriageDecision:
        """Apply Galileo stability patterns for reliability"""
        
        # Adaptive conflict resolution
        if self.stability_patterns.get('adaptive_routing') and decision.action == "clarify":
            # If user has been clarified multiple times, escalate
            if len(context.previous_clarifications) >= 2:
                decision.action = "fallback"
                decision.fallback_response = self._generate_fallback_response(
                    "", "escalation"
                )
                decision.reasoning += " | Escalated after multiple clarifications"
        
        # Context preservation
        if self.stability_patterns.get('context_preservation'):
            if decision.context_summary is None and context.history:
                decision.context_summary = self._generate_context_summary("", context)
        
        # Progressive learning adaptation
        if self.stability_patterns.get('progressive_learning'):
            # Adjust confidence based on historical performance
            if hasattr(self, 'workflow_performance'):
                workflow_performance = getattr(self, 'workflow_performance', {})
                if decision.target_workflow in workflow_performance:
                    performance = workflow_performance[decision.target_workflow]
                    if performance < 0.7:  # Poor historical performance
                        decision.confidence *= 0.8  # Reduce confidence
                        decision.reasoning += f" | Adjusted for historical performance ({performance:.2f})"
        
        return decision

    def _generate_clarification_question(self, 
                                       candidate_workflows: List[str], 
                                       user_query: str, 
                                       context: ConversationContext) -> str:
        """Generate intelligent clarification questions using IBM Watson patterns"""
        
        # Binary choice for 2 candidates
        if len(candidate_workflows) == 2:
            desc1 = self._get_workflow_description(candidate_workflows[0])
            desc2 = self._get_workflow_description(candidate_workflows[1])
            return f"I can help with {desc1} or {desc2}. Which would you prefer?"
        
        # Multiple choice for 3+ candidates
        elif len(candidate_workflows) > 2:
            descriptions = [self._get_workflow_description(wf) for wf in candidate_workflows]
            if len(descriptions) == 3:
                return f"I can help with {descriptions[0]}, {descriptions[1]}, or {descriptions[2]}. Which interests you most?"
            else:
                return f"I can help with several things: {', '.join(descriptions[:-1])}, or {descriptions[-1]}. Which would you like?"
        
        # Single candidate but low confidence
        else:
            desc = self._get_workflow_description(candidate_workflows[0])
            return f"I believe you're asking about {desc}. Is that correct, or did you need something else?"

    def _get_workflow_description(self, workflow_name: str) -> str:
        """Get user-friendly description for workflow"""
        descriptions = {
            'RozieAir_Flight_Booking_Flow': 'booking a new flight',
            'RozieAir_Flight_Status': 'checking your flight status',
            'Case_Status': 'checking a support case status',
            'Name_Correction': 'correcting a passenger name',
            'Prebook_Meal': 'booking a special meal',
            'Baggage_Support': 'baggage assistance or tracking',
        }
        return descriptions.get(workflow_name, workflow_name.replace('_', ' ').lower())

    def _generate_fallback_response(self, user_query: str, fallback_type: str) -> str:
        """Generate intelligent fallback responses using three-tier strategy"""
        
        company_name = self.config.get('company_name', 'our company')
        avatar_name = self.config.get('avatar_name', 'your assistant')
        domain = self.config.get('use_case_domain', 'our services')
        
        if fallback_type == "domain_alternative":
            alternatives = self.triage_intelligence.get('fallback_config', {}).get('domain_alternatives', [])
            if alternatives:
                alt_list = ', '.join(alternatives[:3])
                return f"I specialize in {company_name} {domain.lower()} services and can't directly help with that request. However, I can assist you with {alt_list}. Would any of these be helpful?"
            else:
                return f"I specialize in {company_name} services. Is there something specific about {domain.lower()} I can help you with?"
        
        elif fallback_type == "out_of_scope":
            return f"I'm {avatar_name}, your {company_name} {domain.lower()} specialist. I focus on {domain.lower()}-related questions and services. How can I help you with your {domain.lower()} needs today?"
        
        elif fallback_type == "escalation":
            return f"I want to ensure you get the best possible assistance. Let me connect you with a specialist who can properly address your specific needs."
        
        else:
            return f"I'm here to help with your {company_name} {domain.lower()} needs. Could you tell me more about what you're looking for?"

    def _generate_context_summary(self, user_query: str, context: ConversationContext) -> str:
        """Generate concise context summary for agent handoff"""
        summary_parts = []
        
        if context.history:
            # Extract key information from recent history
            recent_history = " ".join(context.history[-3:])
            if len(recent_history) > 200:
                recent_history = recent_history[:200] + "..."
            summary_parts.append(f"Recent conversation: {recent_history}")
        
        if context.user_preferences:
            prefs = [f"{k}: {v}" for k, v in context.user_preferences.items()]
            summary_parts.append(f"User preferences: {', '.join(prefs[:3])}")
        
        if context.previous_clarifications:
            summary_parts.append(f"Previous clarifications: {len(context.previous_clarifications)}")
        
        return " | ".join(summary_parts) if summary_parts else "New conversation"

    def _update_metrics(self, decision: TriageDecision):
        """Update performance metrics for monitoring"""
        self.metrics['total_decisions'] += 1
        
        if decision.action == "route":
            self.metrics['routing_decisions'] += 1
        elif decision.action == "clarify":
            self.metrics['clarification_decisions'] += 1
        elif decision.action == "fallback":
            self.metrics['fallback_decisions'] += 1
        
        # Update confidence distribution
        if decision.confidence >= self.confidence_thresholds['high']:
            self.metrics['confidence_distribution']['high'] += 1
        elif decision.confidence >= self.confidence_thresholds['medium']:
            self.metrics['confidence_distribution']['medium'] += 1
        else:
            self.metrics['confidence_distribution']['low'] += 1

    def _log_decision(self, user_query: str, decision: TriageDecision):
        """Log decision for debugging and analysis"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'user_query': user_query[:100] + "..." if len(user_query) > 100 else user_query,
            'decision': {
                'action': decision.action,
                'confidence': round(decision.confidence, 3),
                'reasoning': decision.reasoning,
                'target_workflow': decision.target_workflow,
                'metadata': decision.metadata
            },
            'metrics': self.metrics
        }
        
        self.logger.info(f"Triage Decision: {json.dumps(log_data, indent=2)}")
        
        # Console logging for development
        print(f"\n🎯 TRIAGE INTELLIGENCE DECISION")
        print(f"📝 Query: {user_query}")
        print(f"⚡ Action: {decision.action.upper()}")
        print(f"🎯 Confidence: {decision.confidence:.3f}")
        print(f"💭 Reasoning: {decision.reasoning}")
        if decision.target_workflow:
            print(f"🚀 Target: {decision.target_workflow}")
        if decision.clarification_question:
            print(f"❓ Clarification: {decision.clarification_question}")
        print(f"📊 Metrics: {self.metrics['total_decisions']} total, {self.metrics['routing_decisions']} routed")
        print("-" * 60)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        total = self.metrics['total_decisions']
        if total == 0:
            return {"message": "No decisions made yet"}
        
        return {
            "total_decisions": total,
            "routing_rate": self.metrics['routing_decisions'] / total,
            "clarification_rate": self.metrics['clarification_decisions'] / total,
            "fallback_rate": self.metrics['fallback_decisions'] / total,
            "confidence_distribution": {
                k: v / total for k, v in self.metrics['confidence_distribution'].items()
            },
            "performance_grade": self._calculate_performance_grade()
        }

    def _calculate_performance_grade(self) -> str:
        """Calculate overall performance grade"""
        total = self.metrics['total_decisions']
        if total < 10:
            return "Insufficient data"
        
        routing_rate = self.metrics['routing_decisions'] / total
        fallback_rate = self.metrics['fallback_decisions'] / total
        
        if routing_rate > 0.8 and fallback_rate < 0.1:
            return "Excellent (A)"
        elif routing_rate > 0.7 and fallback_rate < 0.15:
            return "Good (B)"
        elif routing_rate > 0.6 and fallback_rate < 0.2:
            return "Fair (C)"
        else:
            return "Needs Improvement (D)" 