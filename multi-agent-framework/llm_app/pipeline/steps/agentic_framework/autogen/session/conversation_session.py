from loguru import logger
from typing import List, Any
from autogen_core.model_context import Buffered<PERSON><PERSON>CompletionContext
from autogen_core.models import LLMMessage, AssistantMessage


class ConversationSession:
    """A class to manage conversation sessions with message history."""

    def __init__(self):
        self._session_context = BufferedChatCompletionContext(buffer_size=500)

    async def add_message(self, message: LLMMessage):
        """Add an message to the conversation history.

        Args:
            message (LLMMessage): llm message to add

        """
        await self._session_context.add_message(message)

    async def clear_messages(self):
        """Clear all messages from the conversation history."""
        await self._session_context.clear()

    async def get_conversation_messages(self):
        """Retrieve all messages from the conversation history.

        Returns:
            list: List of conversation messages
        """
        messages = await self._session_context.get_messages()
        return messages

    async def get_route_agent(self, route_agent):
        """
            Retrieves route agent to send message to.

            CRITICAL FIX: Always route new user messages back to triage agent
            so it can re-evaluate intent and decide on proper routing.

            This fixes the issue where users saying "Something's wrong with my flight"
            during booking flow would incorrectly stay with booking agent.

        Returns:
            str: route agent (always triage for new user messages)
        """
        # ALWAYS route back to triage agent for new user messages
        # This ensures triage can re-evaluate intent and handle context switches
        # like "Something's wrong with my flight" during booking flow
        return route_agent
