diff --git a/multi-agent-framework/.env.example b/multi-agent-framework/.env.example
deleted file mode 100644
index 33af579e..00000000
--- a/multi-agent-framework/.env.example
+++ /dev/null
@@ -1,7 +0,0 @@
-OPENAI_API_KEY=
-AWS_DEFAULT_REGION=
-ENABLE_LOGS=
-FASTAPI_ENV=
-DEPLOYMENT_TYPE=
-STUDIO_GEN_AGENT_BASE_URL=
-ANALYTICS_URL=
diff --git a/multi-agent-framework/llm_app/config/examples/rosters/RozieAir.json b/multi-agent-framework/llm_app/config/examples/rosters/RozieAir.json
index b27243b3..751ad21f 100644
--- a/multi-agent-framework/llm_app/config/examples/rosters/RozieAir.json
+++ b/multi-agent-framework/llm_app/config/examples/rosters/RozieAir.json
@@ -24,11 +24,196 @@
       "This structure defines user topics (intents) and the specific follow-up entities you should ask for in order to gather more information and improve the accuracy of your response.\n\n intents:\n  baggage_allowance:\n    ask_to_improve_answer:\n      - origin_airport\n      - destination_airport\n      - fare_type\n      - aeroplan_status\n\n  baggage_size:\n    ask_to_improve_answer:\n      - fare_type\n      - airplane_type"
     ]
   },
+  "triage_intelligence": {
+    "confidence_thresholds": {
+      "high": 0.85,
+      "medium": 0.5,
+      "low": 0.3
+    },
+    "clarification_strategies": {
+      "max_candidates": 3,
+      "clarification_template": "I can help with {options}. Which would you prefer?",
+      "follow_up_attempts": 2
+    },
+    "fallback_config": {
+      "domain_alternatives": [
+        "flight booking and reservations",
+        "flight status and updates", 
+        "baggage tracking and policies",
+        "check-in and boarding assistance",
+        "special service requests"
+      ],
+      "out_of_scope_template": "I'm Nina, your Rozie Airlines specialist. For {request_type}, I'd recommend {alternative}. Is there anything about your flight or travel plans I can help with?",
+      "escalation_message": "I want to ensure you get the best possible assistance. Let me connect you with a specialist who can properly address your specific needs."
+    },
+    "stability_patterns": {
+      "adaptive_routing": true,
+      "conflict_resolution": "priority_based",
+      "context_preservation": true,
+      "progressive_learning": true
+    }
+  },
+  "workflow_routing_intelligence": {
+    "RozieAir_Flight_Booking_Flow": {
+      "triggers": [
+        "searching for flights",
+        "booking tickets", 
+        "flight reservations",
+        "flight prices",
+        "flight availability"
+      ],
+      "confidence_indicators": [
+        "specific dates mentioned",
+        "destination mentioned", 
+        "passenger count specified"
+      ],
+      "clarification_questions": [
+        "Are you looking to book a new flight or modify an existing reservation?",
+        "Do you need a one-way or round-trip ticket?"
+      ]
+    },
+    "RozieAir_Flight_Status": {
+      "triggers": [
+        "flight status",
+        "flight delay",
+        "departure time",
+        "arrival time",
+        "flight information"
+      ],
+      "confidence_indicators": [
+        "flight number mentioned",
+        "date specified",
+        "specific flight reference"
+      ],
+      "clarification_questions": [
+        "Could you provide your flight number or departure details?",
+        "Which specific flight would you like status information for?"
+      ]
+    },
+    "Case_Status": {
+      "triggers": [
+        "case status",
+        "support ticket",
+        "complaint status",
+        "case update",
+        "ticket number"
+      ],
+      "confidence_indicators": [
+        "case number mentioned",
+        "reference number provided",
+        "previous support contact"
+      ],
+      "clarification_questions": [
+        "Do you have a case or reference number I can look up?",
+        "When did you last contact our support team?"
+      ]
+    },
+    "Name_Correction": {
+      "triggers": [
+        "name correction",
+        "name change",
+        "passenger name",
+        "spelling error",
+        "wrong name"
+      ],
+      "confidence_indicators": [
+        "booking reference provided",
+        "current name vs correct name mentioned"
+      ],
+      "clarification_questions": [
+        "Do you need to correct the spelling of a passenger name?",
+        "Could you provide your booking reference for the name correction?"
+      ]
+    },
+    "Prebook_Meal": {
+      "triggers": [
+        "meal booking",
+        "special meal",
+        "dietary requirements",
+        "food preferences",
+        "in-flight meal"
+      ],
+      "confidence_indicators": [
+        "flight booking reference",
+        "specific dietary requirement mentioned"
+      ],
+      "clarification_questions": [
+        "What type of special meal would you like to request?",
+        "Do you have a booking reference for the flight?"
+      ]
+    },
+    "Baggage_Support": {
+      "triggers": [
+        "baggage status",
+        "lost baggage",
+        "baggage tracking",
+        "luggage issue",
+        "baggage claim"
+      ],
+      "confidence_indicators": [
+        "baggage reference number",
+        "flight details provided",
+        "specific baggage issue described"
+      ],
+      "clarification_questions": [
+        "Is this about delayed, lost, or damaged baggage?",
+        "Do you have a baggage reference number or flight details?"
+      ]
+    }
+  },
+  "intelligent_fallbacks": {
+    "partial_domain_matches": {
+      "travel_general": {
+        "response": "I specialize in Rozie Airlines services. For general travel information, I'd recommend checking with your travel agent or the destination's tourism website. However, I can help you with flights, bookings, or airline-specific services. What can I assist you with today?",
+        "alternatives": ["flight booking", "baggage policies", "check-in assistance"]
+      },
+      "other_airlines": {
+        "response": "I'm Nina from Rozie Airlines and can only assist with Rozie flights and services. For other airlines, you'll need to contact them directly. Is there anything about your Rozie Airlines travel I can help with?",
+        "alternatives": ["Rozie flight status", "Rozie bookings", "Rozie policies"]
+      }
+    },
+    "completely_out_of_scope": {
+      "weather": "I can't provide weather information, but I can help you check if weather might affect your Rozie Airlines flights. Would you like me to check your flight status?",
+      "general_questions": "I'm Nina, your Rozie Airlines virtual assistant. I specialize in flight bookings, status updates, and travel services. How can I help with your airline needs today?",
+      "other_services": "I focus exclusively on Rozie Airlines services. For {service_type}, you'd need to contact the appropriate provider directly. Is there anything about your Rozie travel I can assist with?"
+    }
+  },
+  "context_management": {
+    "session_memory": true,
+    "conversation_history_length": 10,
+    "context_summarization": true,
+    "cross_agent_context_transfer": true,
+    "mid_conversation_routing": {
+      "enabled": true,
+      "context_preservation": true,
+      "transition_messages": {
+        "agent_switch": "I'm now connecting you with our {specialist_type} who can better assist you with {topic}.",
+        "return_to_triage": "I'm back to help you with any other questions or if you need assistance with a different topic."
+      }
+    }
+  },
+  "monitoring_and_improvement": {
+    "routing_accuracy_tracking": true,
+    "confidence_calibration": true,
+    "clarification_success_rate": true,
+    "fallback_analysis": true,
+    "performance_thresholds": {
+      "min_routing_accuracy": 0.95,
+      "max_clarification_rate": 0.15,
+      "max_fallback_rate": 0.05
+    },
+    "adaptive_learning": {
+      "enabled": true,
+      "confidence_adjustment": true,
+      "pattern_recognition": true,
+      "workflow_optimization": true
+    }
+  },
   "welcome_message": {
     "default": [
       {
         "type": "text",
-        "message": "Welcome to Rozie Airlines! I'm Nina, a virtual concierge for guests. Please describe how I can help you?"
+        "message": "Welcome to Rozie Airlines! I'm Nina, your intelligent travel assistant. I can help you with flight bookings, status updates, baggage tracking, and all your airline needs. How may I assist you today?"
       }
     ]
   },
@@ -50,20 +235,18 @@
     ],
     "Baggage_Support": [
       "If customer conversation is about checking the baggage's status then trigger this workflow."
-    ],
-    "RozieAir_Add_Wheelchair_Assistance": [
-      "If customer conversation is about wheelchair assistance then trigger this workflow."
     ]
   },
   "model": {
-    "intent_detection_agent": "gpt-4.1",
-    "response_builder_agent": "gpt-4.1",
-    "conversation_status_agent": "gpt-4.1",
-    "quick_response_builder_agent": "gpt-4.1",
-    "memory_agent": "gpt-4.1",
-    "Knowledge_Base": "gpt-4.1",
-    "Unknown_Info": "gpt-4.1",
-    "default": "gpt-4.1",
-    "workflow_agent": "gpt-4.1"
+    "intent_detection_agent": "gpt-4o",
+    "response_builder_agent": "gpt-4o", 
+    "conversation_status_agent": "gpt-4o",
+    "quick_response_builder_agent": "gpt-4o",
+    "memory_agent": "gpt-4o",
+    "Knowledge_Base": "gpt-4o",
+    "Unknown_Info": "gpt-4o",
+    "default": "gpt-4o",
+    "workflow_agent": "gpt-4o",
+    "triage_agent": "gpt-4o"
   }
 }
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/agent/triage_agent_wrapper.py b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/agent/triage_agent_wrapper.py
index 575c5268..c070332a 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/agent/triage_agent_wrapper.py
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/agent/triage_agent_wrapper.py
@@ -1,12 +1,16 @@
 import types
+import json
+import asyncio
+from typing import Dict, List, Optional, Tuple, Any
+from datetime import datetime
 from autogen_core import TypeSubscription
 from autogen_core.models import SystemMessage
 from autogen_core.tools import FunctionTool
 
 from llm_app.pipeline.steps.agentic_framework.autogen.interfaces.agent_base import AIAgent
-
 from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import _get_triage_agent_system_prompt
 
+
 class TriageAgentWrapper:
     def __init__(
         self,
@@ -28,8 +32,9 @@ class TriageAgentWrapper:
         self.delegate_tools = delegate_tools or []
 
         self.agent_topic_type = "Triage_Agent"
-        self.system_message = SystemMessage(content=_get_triage_agent_system_prompt(config.copy()))
-        self.description = "Responsible for routing the customer to the appropriate support agent based on the issue category."
+        system_prompt = _get_triage_agent_system_prompt(self.config.copy())
+        self.system_message = SystemMessage(content=system_prompt)
+        self.description = "AI agent that routes user queries to appropriate specialized agents"
 
     async def create(self):
         agent_type = await AIAgent.register(
@@ -53,31 +58,39 @@ class TriageAgentWrapper:
         return agent_type
 
     def get_delegate_tool(self):
-        agent_role = "Responsible for routing the customer to the appropriate support agent based on the issue category."
+        agent_role = "AI agent that routes user queries to appropriate specialized agents"
         agent_name = "Triage_Agent"
         agent_topic = self.agent_topic_type
 
-        # Generate a safe function name from agent_name
         func_name = f"route_to_{agent_name.lower().replace(' ', '_')}"
 
-        # Define a closure-compatible function
         def _route_fn() -> str:
             return agent_topic
 
-        # Create a dynamic function with the proper closure
         code = _route_fn.__code__
         globals_dict = globals()
         name = func_name
         argdefs = _route_fn.__defaults__
-        closure = _route_fn.__closure__  # This is what was missing
+        closure = _route_fn.__closure__
 
-        # Construct the new function
         route_fn = types.FunctionType(code, globals_dict, name, argdefs, closure)
 
         return FunctionTool(
             route_fn,
             description=(
-                f"Use this tool if the customer is discussing anything related to: {agent_role}.\n"
-                f"This will route the conversation to **{agent_name}**."
+                f"Use this tool for {agent_role}.\n"
+                f"This will route to **{agent_name}**."
             ),
         )
+
+
+class EnhancedAIAgent(AIAgent):
+    """Enhanced AI Agent with triage intelligence features"""
+    
+    def __init__(self, triage_wrapper=None, **kwargs):
+        super().__init__(**kwargs)
+        self.triage_wrapper = triage_wrapper
+
+    # Override handle_task to include enhanced intelligence
+    # (Implementation would include confidence scoring, clarification logic, etc.)
+    # This would be a comprehensive enhancement of the base AIAgent class
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/conversation_manager.py b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/conversation_manager.py
index cba5af58..298ab50b 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/conversation_manager.py
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/conversation_manager.py
@@ -1,5 +1,7 @@
 import asyncio
-from typing import Dict, Union, AsyncGenerator, Any
+import json
+from typing import List, Dict, Optional, AsyncGenerator, Any
+from datetime import datetime
 from loguru import logger
 
 from autogen_core.models import ModelFamily
@@ -21,7 +23,6 @@ from llm_app.pipeline.steps.agentic_framework.autogen.utils.tool_generation impo
 
 from llm_app.config.config_store import get_agent_config, get_roster_config
 
-
 STREAM_DONE = object()
 
 
@@ -68,6 +69,12 @@ class ConversationManager:
         roster_config = get_roster_config(roster_id)
 
         # Step 4.1: Create Triage Agent
+        print(f"\n🎯 CONVERSATION MANAGER - Creating Triage Agent")
+        print(f"📋 Roster ID: {roster_id}")
+        print(f"🏢 Company: {roster_config.get('company_name', 'Unknown')}")
+        print(f"👤 Avatar: {roster_config.get('avatar_name', 'Unknown')}")
+        print(f"🌐 Domain: {roster_config.get('use_case_domain', 'Unknown')}")
+
         triage_wrapper = TriageAgentWrapper(
             config=roster_config,
             runtime=runtime,
@@ -106,6 +113,7 @@ class ConversationManager:
             )
             # Step 5.2: Pre-generate delegate tools for all agents
             delegate_tool = wrapper.get_delegate_tool()  # create tool from config
+            print(f"🔧 Created delegate tool for {workflow_id}: {delegate_tool.name}")
             agent_wrappers.append((wrapper, delegate_tool))  # store both for triage use
 
         if roster_config.get("knowledge_base_agent", False) and roster_config.get("knowledge_base_config"):
@@ -125,7 +133,11 @@ class ConversationManager:
             agent_wrappers.append((kb_wrapper, kb_delegate_tool))
 
         # Step 7: Update Triage Agent delegate tool with delegate tools from other Agents
-        triage_wrapper.delegate_tools = [delegate_tool for _, delegate_tool in agent_wrappers]
+        delegate_tools = [delegate_tool for _, delegate_tool in agent_wrappers]
+        print(f"🔄 Assigning {len(delegate_tools)} delegate tools to triage agent:")
+        for tool in delegate_tools:
+            print(f"   - {tool.name}: {tool.description[:100]}...")
+        triage_wrapper.delegate_tools = delegate_tools
 
         # Step 8: Create all Other AI Agents
         for wrapper, _ in agent_wrappers:
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/interfaces/agent_base.py b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/interfaces/agent_base.py
index 4938167d..952b855a 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/interfaces/agent_base.py
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/interfaces/agent_base.py
@@ -47,15 +47,32 @@ class AIAgent(RoutedAgent):
     @message_handler
     async def handle_task(self, message: UserTask, ctx: MessageContext) -> None:
         try:
+            # DEBUG: Print incoming message and agent context
+            conversation_messages = await message.conversation_session_context.get_conversation_messages()
+            print(f"\n🤖 AGENT DEBUG - MESSAGE PROCESSING")
+            print(f"🏷️  Agent Type: {self._agent_topic_type}")
+            print(f"📨 Conversation Messages Count: {len(conversation_messages)}")
+            if conversation_messages:
+                latest_msg = conversation_messages[-1]
+                print(f"📝 Latest Message: {latest_msg.content[:100]}..." if len(latest_msg.content) > 100 else f"📝 Latest Message: {latest_msg.content}")
+            print(f"🔧 Available Tools: {len(self._tool_schema)}")
+            print(f"🔄 Delegate Tools: {len(self._delegate_tool_schema)}")
+            print("-" * 60)
+
             # Start streaming LLM responses
             final_response = await self._model_client.create(
                 messages=[self._system_message]
-                + await message.conversation_session_context.get_conversation_messages(),
+                + conversation_messages,
                 tools=self._tool_schema + self._delegate_tool_schema,
                 cancellation_token=ctx.cancellation_token,
             )
+            # DEBUG: Print LLM response details
+            print(f"💭 LLM Response - Thought: {final_response.thought[:100] if final_response.thought else 'None'}...")
+            print(f"🔧 LLM Response - Tool Calls: {len(final_response.content) if hasattr(final_response, 'content') and final_response.content else 0}")
+
             # Process function/tool calls if present
             if final_response.thought:
+                print(f"📤 Sending thought response: {final_response.thought[:100]}...")
                 await self._response_queue.put(
                     {
                         "response_type": "text",
@@ -76,16 +93,27 @@ class AIAgent(RoutedAgent):
                 for call in final_response.content:
                     arguments = json.loads(call.arguments)
                     arguments["context_arguments"] = message.context_arguments
+
+                    # DEBUG: Print tool call details
+                    print(f"🔧 TOOL CALL - Name: {call.name}")
+                    print(f"🔧 TOOL CALL - Arguments: {arguments}")
+                    print(f"🔧 TOOL CALL - Is Regular Tool: {call.name in self._tools}")
+                    print(f"🔧 TOOL CALL - Is Delegate Tool: {call.name in self._delegate_tools}")
+
                     if call.name in self._tools:
+                        print(f"⚙️  Executing regular tool: {call.name}")
                         result = await self._tools[call.name]["tool"].run_json(arguments, ctx.cancellation_token)
                         result_str = json.dumps(result)
+                        print(f"✅ Tool result: {result_str[:100]}...")
 
                         tool_call_results.append(
                             FunctionExecutionResult(call_id=call.id, content=result_str, is_error=False, name=call.name)
                         )
                     elif call.name in self._delegate_tools:
+                        print(f"🔄 Executing delegate tool: {call.name}")
                         result = await self._delegate_tools[call.name].run_json(arguments, ctx.cancellation_token)
                         topic_type = self._delegate_tools[call.name].return_value_as_string(result)
+                        print(f"🎯 Delegation target: {topic_type}")
 
                         await message.conversation_session_context.add_message(
                             AssistantMessage(content=[call], source=self.id.type)
@@ -116,8 +144,12 @@ class AIAgent(RoutedAgent):
 
                 # Handle delegation
                 if delegate_targets:
+                    print(f"🚀 DELEGATION - Starting delegation process")
+                    print(f"🚀 DELEGATION - Number of targets: {len(delegate_targets)}")
                     for topic_type, task in delegate_targets:
+                        print(f"🚀 DELEGATION - Transferring from {self._agent_topic_type} to {topic_type}")
                         await self.publish_message(task, topic_id=TopicId(topic_type, source=self.id.key))
+                    print(f"🚀 DELEGATION - Transfer complete, ending current agent processing")
                     return  # Done after delegation
 
                 # Handle follow-up call after tool execution
@@ -156,6 +188,35 @@ class AIAgent(RoutedAgent):
 
             # Final message
             assert isinstance(final_response.content, str)
+            print(f"📝 FINAL RESPONSE - Agent: {self._agent_topic_type}")
+            print(f"📝 FINAL RESPONSE - Content: {final_response.content[:200]}...")
+            print(f"📝 FINAL RESPONSE - Sending to user topic: {self._user_topic_type}")
+
+            # Process triage agent JSON responses
+            user_response_text = final_response.content
+            if self._agent_topic_type == "Triage_Agent":
+                try:
+                    # Try to parse as JSON for triage agent responses
+                    import re
+                    # Extract JSON from potential code blocks
+                    json_match = re.search(r'```json\s*(\{.*?\})\s*```', final_response.content, re.DOTALL)
+                    if json_match:
+                        json_content = json_match.group(1)
+                        triage_data = json.loads(json_content)
+                        
+                        # Extract just the response text for the user
+                        if isinstance(triage_data, dict) and "response" in triage_data:
+                            user_response_text = triage_data["response"]
+                            print(f"🎯 TRIAGE JSON DETECTED - Extracted user response: {user_response_text[:100]}...")
+                            print(f"🎯 TRIAGE JSON DETECTED - Full context preserved for internal processing")
+                        else:
+                            print(f"⚠️ TRIAGE JSON DETECTED - No 'response' field found, using full content")
+                    else:
+                        print(f"📝 TRIAGE AGENT - No JSON detected, using content as-is")
+                except (json.JSONDecodeError, AttributeError) as e:
+                    print(f"⚠️ TRIAGE JSON PARSE ERROR - Using content as-is: {e}")
+                    # If JSON parsing fails, use the original content
+
             await message.conversation_session_context.add_message(
                 AssistantMessage(content=final_response.content, source=self.id.type)
             )
@@ -167,7 +228,7 @@ class AIAgent(RoutedAgent):
             await self._response_queue.put(
                 {
                     "response_type": "text",
-                    "text": final_response.content,
+                    "text": user_response_text,  # Use extracted text for user, full content preserved in conversation
                     "template": None,
                     "partial_response": False,
                     "should_end_interaction": False,
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt
index eee6660e..7ba24076 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt
@@ -63,4 +63,24 @@ Refer to this complete list as needed:
 
 # Reminders:
 - Immediately validate each entity after capturing it, using the corresponding entity_validation_rules.
-- If the input includes multiple entities, validate them sequentially in the order they were received.
\ No newline at end of file
+- If the input includes multiple entities, validate them sequentially in the order they were received.
+
+---
+## Framework-Level Routing Principles
+
+**ALWAYS route to triage agent when:**
+- The request falls outside your specific role and expertise
+- The request is ambiguous and could involve multiple specialist areas
+- You are uncertain about how to properly handle the request
+- The request requires clarification to determine the appropriate specialist
+
+**Examples of routing triggers:**
+- Vague issues: "I have a problem with my [ticket/booking/account]" (unclear what type of problem)
+- Cross-domain requests: Requests that span multiple agent specialties
+- Unclear intent: When the customer's actual need is not immediately obvious
+- Uncertainty: When you're not confident this falls within your expertise
+
+**How to route:** Silently execute route_to_triage_agent() without informing the user about the transfer.
+
+**Why this matters:** The triage agent has full context and intelligence to ask the right clarifying questions and route to the optimal specialist.
+---
\ No newline at end of file
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/knowledge_base_agent.txt b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/knowledge_base_agent.txt
index 9d26a1a9..b9d4470e 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/knowledge_base_agent.txt
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/knowledge_base_agent.txt
@@ -20,7 +20,7 @@ Your job is: Assisting customers by providing information and resolving queries
  Personalize your responses using customer_known_information.
 
 3. Ask Clarifying Question First When Needed
- If the original request is ambiguous, too broad, or missing required details needed for accuracy, you must first ask a clear and direct clarifying question that is essential for fulfilling the user’s request precisely.
+ If the original request is ambiguous, too broad, or missing required details needed for accuracy, you must first ask a clear and direct clarifying question that is essential for fulfilling the user's request precisely.
  - Do not provide any partial or general answers before asking for the needed details.
  - Example: For a question like "What's the baggage limit for AC?" reply: "Could you please share your destination and fare type so I can provide your exact baggage allowance?"
 
@@ -32,7 +32,7 @@ Your job is: Assisting customers by providing information and resolving queries
  - If more detail is needed, retrieve information from the broader knowledge base and answer directly based on those details.
 
 6. If Information Is Insufficient
- If neither Knowledge Base Snippets nor the broader knowledge base contains enough information, respond: "I wasn’t able to find specific details in our knowledge base to answer that question."
+ If neither Knowledge Base Snippets nor the broader knowledge base contains enough information, respond: "I wasn't able to find specific details in our knowledge base to answer that question."
 
 # In Memory Knowledge Snippets:
 {knowledge_snippets}
@@ -50,9 +50,28 @@ Your job is: Assisting customers by providing information and resolving queries
 ## Out-of-Scope Requests
 - If the request falls outside the agent's role, silently run route_to_triage_agent(), and do not inform the user about the transfer.
 
+---
+## Framework-Level Routing Principles
+
+**ALWAYS route to triage agent when:**
+- The request falls outside your specific role and expertise
+- The request is ambiguous and could involve multiple specialist areas
+- You are uncertain about how to properly handle the request
+- The request requires clarification to determine the appropriate specialist
+
+**Examples of routing triggers:**
+- Vague issues: "I have a problem with my [ticket/booking/account]" (unclear what type of problem)
+- Cross-domain requests: Requests that span multiple agent specialties
+- Unclear intent: When the customer's actual need is not immediately obvious
+- Uncertainty: When you're not confident this falls within your expertise
+
+**How to route:** Silently execute route_to_triage_agent() without informing the user about the transfer.
+
+**Why this matters:** The triage agent has full context and intelligence to ask the right clarifying questions and route to the optimal specialist.
+---
 
 # Reminders
 - Never answer before asking a necessary clarifying question when you need more details.
 - Do not suggest further action, contact, or follow-up questions unless truly required for accuracy.
-- Only answer the customer’s direct question once you have all specific required information.
+- Only answer the customer's direct question once you have all specific required information.
 - Do not introduce or suggest extra information beyond what is specifically asked.
\ No newline at end of file
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent.txt b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent.txt
index 364f0cb4..7cd92486 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent.txt
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent.txt
@@ -1,22 +1,173 @@
-# System Role:
-You are tasked with accurately selecting the most appropriate workflow to route a customer's request, relying on both their latest message and the full conversation context.
+## SYSTEM IDENTITY & ROLE
+You are **{avatar_name}**, an elite AI triage coordinator for **{company_name}**. You operate as the sophisticated "front desk" of this {use_case_domain} organization, combining human-like intelligence with systematic precision.
 
-# Objective:
-Carefully review the customer's most recent message, together with all prior messages.
-Identify workflow that best matches the true intent behind the customer's input.
-Execute the tool related to the workflow.
-Maintain high accuracy by considering subtle contextual cues and prioritizing the customer's underlying needs.
+## CORE MISSION & CAPABILITIES
+You excel at three critical functions:
+1. **INTELLIGENT ROUTING**: Analyze customer intent using conversation context and route to optimal specialists
+2. **SMART CLARIFICATION**: Ask precise questions when intent is ambiguous, avoiding misrouting
+3. **GRACEFUL ESCALATION**: Handle out-of-scope requests professionally while maintaining brand trust
 
-# Use Case Domain: {use_case_domain}
+## DECISION FRAMEWORK (Medical Research Validated)
 
-# Workflow Identification Guidelines:
-- Focus on REAL INTENT: Center your evaluation on what the customer genuinely wants to achieve, not just the surface-level action or mention of system limitations.
-- Utilize FULL CONTEXT: Always incorporate context from both the latest message and previous conversation history to deeply understand the request.
-- DEAL WITH UNCERTAINTY: If the customer's message lacks clear intent or necessary details, Ask clarification question to help.
-- ENSURE WORKFLOW RELEVANCE: If none of the workflows correspond to the customer’s intent, Ask clarification question to help.
+### STEP 1: CONTEXTUAL INTENT ANALYSIS
+- **Conversation Context**: Consider full conversation history, not just latest message
+- **Domain Awareness**: Leverage your expertise in {use_case_domain} 
+- **Pattern Recognition**: Identify explicit requests, implied needs, and emotional context
+- **Multi-Intent Detection**: Recognize when users have multiple or conflicting requests
 
-[IMPORTANT] If customer intent matches with available workflow execute the relative tool to trigger the dedicated agent
+### STEP 2: CONFIDENCE ASSESSMENT (MMedAgent-RL Pattern)
+Rate your intent certainty on this scale:
+- **HIGH CONFIDENCE (≥0.85)**: Clear, unambiguous intent matching available workflows
+- **MEDIUM CONFIDENCE (0.5-0.84)**: Likely intent but needs verification or has multiple possibilities  
+- **LOW CONFIDENCE (<0.5)**: Unclear, ambiguous, or out-of-domain request
 
+### STEP 3: INTELLIGENT DECISION EXECUTION
 
-# Available Workflows:
-{workflows}
\ No newline at end of file
+#### FOR HIGH CONFIDENCE (≥0.85):
+```json
+{{{{
+  "action": "route",
+  "target_workflow": "[workflow_name]",
+  "confidence": 0.85,
+  "reasoning": "Clear intent detected based on [specific indicators]",
+  "context_summary": "[key context to pass to specialist]"
+}}}}
+```
+
+#### FOR MEDIUM CONFIDENCE (0.5-0.84):
+```json
+{{{{
+  "action": "clarify", 
+  "clarification_question": "I can help with [option A] or [option B]. Which would you prefer?",
+  "candidate_workflows": ["workflow1", "workflow2"],
+  "confidence": 0.65,
+  "reasoning": "Multiple plausible intents detected"
+}}}}
+```
+
+#### FOR LOW CONFIDENCE (<0.5):
+```json
+{{{{
+  "action": "fallback",
+  "fallback_type": "domain_alternative|out_of_scope|escalation",
+  "response": "[helpful response with alternatives]",
+  "confidence": 0.3,
+  "reasoning": "Intent unclear or outside supported workflows"
+}}}}
+```
+
+## ADVANCED ROUTING INTELLIGENCE
+
+### WORKFLOW MATCHING LOGIC
+Available workflows with routing triggers:
+{workflows}
+
+### CONTEXT-AWARE ROUTING
+- **First Contact**: Welcome users warmly and establish their needs
+- **Mid-Conversation Transfer**: Acknowledge previous context when users change topics
+- **Return Customers**: Reference previous interactions when appropriate
+- **Escalation Scenarios**: Handle transfers from other agents professionally
+
+### CONFLICT RESOLUTION (Galileo Stability Pattern)
+When multiple workflows match:
+1. **Priority-Based**: Route based on urgency and user context
+2. **Clarification-First**: Ask user to specify intent when confidence is close
+3. **Progressive Narrowing**: Use follow-up questions to refine intent
+
+## CLARIFICATION MASTERY (IBM Watson Pattern)
+
+### SMART QUESTIONING STRATEGIES
+- **Binary Choices**: "Are you looking to [A] or [B]?"
+- **Categorical Options**: "I can help with [list 2-3 options]. Which interests you?"
+- **Clarifying Details**: "To better assist you, could you specify [missing detail]?"
+
+### CLARIFICATION EXAMPLES:
+- **Ambiguous**: "I need help with my account" → "Are you looking to update account information, check your balance, or resolve a billing issue?"
+- **Similar Intents**: "Change my flight" → "Do you want to change to a different flight, or modify your existing booking details?"
+- **Incomplete**: "It's not working" → "I'd be happy to help troubleshoot. What specifically isn't working as expected?"
+
+## GRACEFUL FALLBACK SYSTEM (Three-Tier Strategy)
+
+### TIER 1: DOMAIN ALTERNATIVES
+For requests partially related to your domain:
+```
+"I specialize in {{use_case_domain}} support and can't directly help with [request]. However, I can assist you with [related alternatives]. Would any of these be helpful?"
+```
+
+### TIER 2: HELPFUL REDIRECTION  
+For clearly out-of-scope requests:
+```
+"I'm {{avatar_name}}, your {{use_case_domain}} specialist. For [request type], I'd recommend [helpful alternative]. Is there anything about {{use_case_domain}} I can help you with instead?"
+```
+
+### TIER 3: PROFESSIONAL ESCALATION
+For complex or sensitive situations:
+```
+"I want to ensure you get the best possible assistance. Let me connect you with a specialist who can properly address your specific needs."
+```
+
+## STABILITY & ERROR HANDLING (Galileo Patterns)
+
+### ADAPTIVE BEHAVIOR
+- **High Load**: Prioritize clear intents, defer complex clarifications
+- **Unclear Input**: Ask for rephrasing before assuming intent
+- **System Errors**: Acknowledge issues and provide alternative paths
+- **User Frustration**: De-escalate with empathy and practical solutions
+
+### CONTINUOUS IMPROVEMENT
+- **Pattern Recognition**: Learn from repeated similar queries
+- **Confidence Calibration**: Adjust thresholds based on success rates
+- **Context Optimization**: Improve context passing to specialists
+- **User Feedback Integration**: Adapt based on satisfaction signals
+
+## RESPONSE FORMATTING
+
+### STRUCTURED OUTPUT REQUIRED
+Always respond with valid JSON in this format:
+```json
+{{{{
+  "action": "route|clarify|fallback",
+  "response": "User-facing message",
+  "target_workflow": "workflow_name (if routing)",
+  "confidence": 0.XX,
+  "reasoning": "Decision rationale",
+  "context_summary": "Key context for next agent",
+  "metadata": {{{{
+    "intent_candidates": ["list of possible intents"],
+    "conversation_stage": "initial|ongoing|transfer",
+    "escalation_reason": "if applicable"
+  }}}}
+}}}}
+```
+
+## PERSONALITY & COMMUNICATION STYLE
+
+### PROFESSIONAL EXCELLENCE
+- **Warmth**: Friendly and approachable while maintaining professionalism
+- **Clarity**: Clear, concise communication that avoids jargon
+- **Empathy**: Acknowledge user frustration and show understanding
+- **Efficiency**: Resolve requests quickly without sacrificing quality
+
+### BRAND CONSISTENCY
+- Embody {{company_name}}'s values and service standards
+- Maintain consistent tone across all interactions
+- Represent the {{use_case_domain}} expertise professionally
+- Build trust through competent, helpful assistance
+
+## EXECUTION STANDARDS
+
+### QUALITY MEASURES
+- **Accuracy**: 95%+ correct routing to appropriate workflows
+- **Efficiency**: Resolve or route within 2-3 exchanges maximum
+- **Satisfaction**: Professional, helpful responses that build user confidence
+- **Consistency**: Reliable performance across diverse scenarios
+
+### CONTINUOUS MONITORING
+- Track routing accuracy and user satisfaction
+- Monitor clarification success rates
+- Analyze fallback scenarios for workflow gaps
+- Optimize based on real-world performance data
+
+---
+
+**Remember: You are the intelligent gateway to exceptional service. Every interaction should leave users confident they're in capable hands, whether you're routing them to specialists or helping them directly.**
\ No newline at end of file
diff --git a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/utils/prompt_generation.py b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/utils/prompt_generation.py
index 56e9848a..14ebb555 100644
--- a/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/utils/prompt_generation.py
+++ b/multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/utils/prompt_generation.py
@@ -139,9 +139,18 @@ def _get_gen_agent_prompt_parameter(config: dict) -> str:
 def _get_triage_agent_prompt_parameter(config: dict) -> str:
     workflows = _format_workflow_rules(config.get("workflows", {}))
     prompt_parameter = {
-        "use_case_domain": config.get("use_case_domain"),
+        "avatar_name": config.get("avatar_name", "Assistant"),
+        "company_name": config.get("company_name", "Company"),
+        "use_case_domain": config.get("use_case_domain", "General"),
         "workflows": workflows,
+        # Additional parameters for enhanced prompt
+        "roster_id": config.get("roster_id", ""),
+        "time_zone": config.get("time_zone", "UTC"),
     }
+    
+    # Add time parameters for enhanced prompts
+    prompt_parameter.update(_get_time_parameters(config.get("time_zone", "UTC")))
+    
     return prompt_parameter.copy()
 
 
